import java.util.*;

/**
 * Test class to verify the Statistics Console App functionality
 */
public class TestStatistics {
    public static void main(String[] args) {
        System.out.println("=== Testing Statistics Console App ===");
        
        // Test data
        List<Double> testData = Arrays.asList(1.0, 2.0, 3.0, 4.0, 5.0, 3.0, 3.0);
        
        System.out.println("Test Data: " + testData);
        System.out.println();
        
        // Test Mean Calculator
        StatOperation meanCalc = new MeanCalculator(testData);
        double mean = meanCalc.calculate();
        System.out.println("Mean: " + mean);
        System.out.println("Expected: " + (1+2+3+4+5+3+3)/7.0);
        System.out.println("Test Mean: " + (Math.abs(mean - 3.0) < 0.001 ? "PASS" : "FAIL"));
        System.out.println();
        
        // Test Median Calculator
        StatOperation medianCalc = new MedianCalculator(testData);
        double median = medianCalc.calculate();
        System.out.println("Median: " + median);
        System.out.println("Expected: 3.0 (middle value of sorted: 1,2,3,3,3,4,5)");
        System.out.println("Test Median: " + (Math.abs(median - 3.0) < 0.001 ? "PASS" : "FAIL"));
        System.out.println();
        
        // Test Mode Calculator
        StatOperation modeCalc = new ModeCalculator(testData);
        double mode = modeCalc.calculate();
        System.out.println("Mode: " + mode);
        System.out.println("Expected: 3.0 (appears 3 times)");
        System.out.println("Test Mode: " + (Math.abs(mode - 3.0) < 0.001 ? "PASS" : "FAIL"));
        System.out.println();
        
        // Test StatisticsCalculator
        StatisticsCalculator calc = new StatisticsCalculator();
        calc.addNumber(10.0);
        calc.addNumber(20.0);
        calc.addNumber(30.0);
        
        System.out.println("StatisticsCalculator Test:");
        System.out.println("Size: " + calc.getSize() + " (Expected: 3)");
        System.out.println("Test Size: " + (calc.getSize() == 3 ? "PASS" : "FAIL"));
        
        calc.displayData();
        
        System.out.println();
        System.out.println("=== All Basic Tests Completed ===");
        System.out.println("The Statistics Console App is working correctly!");
        System.out.println();
        System.out.println("To run the interactive version, use: java StatisticsConsoleApp");
    }
}
