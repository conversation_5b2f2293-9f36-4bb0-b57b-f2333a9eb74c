import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Calculator class for computing the median
 * Implements StatOperation interface
 */
public class MedianCalculator implements StatOperation {
    private List<Double> data;
    
    /**
     * Constructor takes the data list
     * @param data the list of numbers
     */
    public MedianCalculator(List<Double> data) {
        this.data = data;
    }
    
    /**
     * Calculate the median of the data
     * @return the median value
     */
    @Override
    public double calculate() {
        if (data == null || data.isEmpty()) {
            return 0.0;
        }
        
        // Create a copy and sort it
        List<Double> sortedData = new ArrayList<>(data);
        Collections.sort(sortedData);
        
        int size = sortedData.size();
        
        // If odd number of elements, return middle element
        if (size % 2 == 1) {
            return sortedData.get(size / 2);
        } 
        // If even number of elements, return average of two middle elements
        else {
            int middleIndex1 = size / 2 - 1;
            int middleIndex2 = size / 2;
            return (sortedData.get(middleIndex1) + sortedData.get(middleIndex2)) / 2.0;
        }
    }
}
