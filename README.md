# Statistics Console App

A Java console application that calculates statistical measures (Mean, Median, Mode) using Object-Oriented Programming concepts.

## OOP Concepts Implemented

### 1. **Encapsulation**
- `StatisticsCalculator` class encapsulates the data list and provides controlled access through methods
- Private data members with public getter/setter methods

### 2. **Interface**
- `StatOperation` interface defines the contract with `calculate()` method
- All calculator classes implement this interface

### 3. **Inheritance/Implementation**
- `MeanCalculator`, `MedianCalculator`, and `ModeCalculator` classes implement `StatOperation` interface
- Each class provides its own implementation of the `calculate()` method

### 4. **Polymorphism**
- Different calculator objects are used based on user selection
- Same interface (`StatOperation`) but different behaviors
- Demonstrated in `calculateAndDisplay()` method

## Files Structure

- `StatOperation.java` - Interface defining the calculate() method
- `StatisticsCalculator.java` - Main class that manages the data list
- `MeanCalculator.java` - Calculates arithmetic mean
- `MedianCalculator.java` - Calculates median value
- `ModeCalculator.java` - Calculates mode (most frequent value)
- `StatisticsApp.java` - Main application with console interface

## How to Run

1. **Compile all Java files:**
   ```bash
   javac *.java
   ```

2. **Run the application:**
   ```bash
   java StatisticsApp
   ```

## Features

- **Input Numbers**: Add numbers to the data list
- **Display Data**: View current numbers and count
- **Calculate Statistics**: 
  - Mean (average)
  - Median (middle value)
  - Mode (most frequent value)
  - All statistics at once
- **Clear Data**: Remove all numbers from the list
- **User-friendly Menu**: Easy navigation through console interface

## Sample Usage

1. Start the application
2. Choose "1" to input numbers
3. Enter numbers one by one, type "done" when finished
4. Choose "3" to calculate statistics
5. Select which statistic to calculate or choose "4" for all

## Basic Logic Used

- **Mean**: Sum of all numbers divided by count
- **Median**: Middle value when numbers are sorted (average of two middle values for even count)
- **Mode**: Number that appears most frequently in the dataset

The application uses basic Java concepts and avoids advanced features, making it suitable for learning OOP fundamentals.
