import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Calculator class for computing the mode
 * Implements StatOperation interface
 */
public class ModeCalculator implements StatOperation {
    private List<Double> data;
    
    /**
     * Constructor takes the data list
     * @param data the list of numbers
     */
    public ModeCalculator(List<Double> data) {
        this.data = data;
    }
    
    /**
     * Calculate the mode of the data
     * @return the mode value (most frequent number)
     */
    @Override
    public double calculate() {
        if (data == null || data.isEmpty()) {
            return 0.0;
        }
        
        // Count frequency of each number
        Map<Double, Integer> frequencyMap = new HashMap<>();
        for (double number : data) {
            frequencyMap.put(number, frequencyMap.getOrDefault(number, 0) + 1);
        }
        
        // Find the number with highest frequency
        double mode = data.get(0);
        int maxFrequency = 0;
        
        for (Map.Entry<Double, Integer> entry : frequencyMap.entrySet()) {
            if (entry.getValue() > maxFrequency) {
                maxFrequency = entry.getValue();
                mode = entry.getKey();
            }
        }
        
        return mode;
    }
}
