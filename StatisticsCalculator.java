import java.util.ArrayList;
import java.util.List;

/**
 * Main class that encapsulates the data list
 * Provides methods to manage the statistical data
 */
public class StatisticsCalculator {
    private List<Double> dataList;
    
    /**
     * Constructor initializes empty data list
     */
    public StatisticsCalculator() {
        this.dataList = new ArrayList<>();
    }
    
    /**
     * Add a number to the data list
     * @param number the number to add
     */
    public void addNumber(double number) {
        dataList.add(number);
    }
    
    /**
     * Get the data list
     * @return copy of the data list
     */
    public List<Double> getDataList() {
        return new ArrayList<>(dataList);
    }
    
    /**
     * Get the size of data list
     * @return number of elements in the list
     */
    public int getSize() {
        return dataList.size();
    }
    
    /**
     * Check if data list is empty
     * @return true if empty, false otherwise
     */
    public boolean isEmpty() {
        return dataList.isEmpty();
    }
    
    /**
     * Clear all data from the list
     */
    public void clearData() {
        dataList.clear();
    }
    
    /**
     * Display all numbers in the data list
     */
    public void displayData() {
        if (isEmpty()) {
            System.out.println("No data available.");
            return;
        }
        
        System.out.print("Data: ");
        for (int i = 0; i < dataList.size(); i++) {
            System.out.print(dataList.get(i));
            if (i < dataList.size() - 1) {
                System.out.print(", ");
            }
        }
        System.out.println();
    }
}
