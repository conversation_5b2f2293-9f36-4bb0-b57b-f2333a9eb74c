import java.util.List;

/**
 * Calculator class for computing the mean (average)
 * Implements StatOperation interface
 */
public class MeanCalculator implements StatOperation {
    private List<Double> data;
    
    /**
     * Constructor takes the data list
     * @param data the list of numbers
     */
    public MeanCalculator(List<Double> data) {
        this.data = data;
    }
    
    /**
     * Calculate the mean of the data
     * @return the mean value
     */
    @Override
    public double calculate() {
        if (data == null || data.isEmpty()) {
            return 0.0;
        }
        
        double sum = 0.0;
        for (double number : data) {
            sum += number;
        }
        
        return sum / data.size();
    }
}
