import java.util.Scanner;

/**
 * Main application class for the Statistics Console App
 * Demonstrates polymorphism through StatOperation interface
 */
public class StatisticsApp {
    private StatisticsCalculator calculator;
    private Scanner scanner;
    
    /**
     * Constructor initializes the calculator and scanner
     */
    public StatisticsApp() {
        this.calculator = new StatisticsCalculator();
        this.scanner = new Scanner(System.in);
    }
    
    /**
     * Main method to run the application
     */
    public static void main(String[] args) {
        StatisticsApp app = new StatisticsApp();
        app.run();
    }
    
    /**
     * Main application loop
     */
    public void run() {
        System.out.println("=== Statistics Console App ===");
        System.out.println("Calculate Mean, Median, and Mode of numbers");
        System.out.println();
        
        while (true) {
            displayMenu();
            int choice = getChoice();
            
            switch (choice) {
                case 1:
                    inputNumbers();
                    break;
                case 2:
                    displayData();
                    break;
                case 3:
                    calculateStatistic();
                    break;
                case 4:
                    clearData();
                    break;
                case 5:
                    System.out.println("Thank you for using Statistics Console App!");
                    return;
                default:
                    System.out.println("Invalid choice. Please try again.");
            }
            System.out.println();
        }
    }
    
    /**
     * Display the main menu
     */
    private void displayMenu() {
        System.out.println("--- Menu ---");
        System.out.println("1. Input Numbers");
        System.out.println("2. Display Current Data");
        System.out.println("3. Calculate Statistics");
        System.out.println("4. Clear Data");
        System.out.println("5. Exit");
        System.out.print("Enter your choice (1-5): ");
    }
    
    /**
     * Get user's menu choice
     * @return the selected choice
     */
    private int getChoice() {
        try {
            return Integer.parseInt(scanner.nextLine());
        } catch (NumberFormatException e) {
            return -1;
        }
    }
    
    /**
     * Input numbers from user
     */
    private void inputNumbers() {
        System.out.println("Enter numbers (type 'done' when finished):");
        
        while (true) {
            System.out.print("Enter number: ");
            String input = scanner.nextLine();
            
            if (input.equalsIgnoreCase("done")) {
                break;
            }
            
            try {
                double number = Double.parseDouble(input);
                calculator.addNumber(number);
                System.out.println("Added: " + number);
            } catch (NumberFormatException e) {
                System.out.println("Invalid number. Please try again.");
            }
        }
        
        System.out.println("Numbers added successfully!");
    }
    
    /**
     * Display current data
     */
    private void displayData() {
        System.out.println("--- Current Data ---");
        calculator.displayData();
        System.out.println("Total numbers: " + calculator.getSize());
    }
    
    /**
     * Calculate and display statistics using polymorphism
     */
    private void calculateStatistic() {
        if (calculator.isEmpty()) {
            System.out.println("No data available. Please input numbers first.");
            return;
        }
        
        System.out.println("--- Statistics Menu ---");
        System.out.println("1. Calculate Mean");
        System.out.println("2. Calculate Median");
        System.out.println("3. Calculate Mode");
        System.out.println("4. Calculate All");
        System.out.print("Enter your choice (1-4): ");
        
        int choice = getChoice();
        
        switch (choice) {
            case 1:
                calculateAndDisplay(new MeanCalculator(calculator.getDataList()), "Mean");
                break;
            case 2:
                calculateAndDisplay(new MedianCalculator(calculator.getDataList()), "Median");
                break;
            case 3:
                calculateAndDisplay(new ModeCalculator(calculator.getDataList()), "Mode");
                break;
            case 4:
                calculateAll();
                break;
            default:
                System.out.println("Invalid choice.");
        }
    }
    
    /**
     * Calculate and display a specific statistic (demonstrates polymorphism)
     * @param operation the statistical operation to perform
     * @param name the name of the statistic
     */
    private void calculateAndDisplay(StatOperation operation, String name) {
        double result = operation.calculate();
        System.out.println(name + ": " + result);
    }
    
    /**
     * Calculate and display all statistics
     */
    private void calculateAll() {
        System.out.println("--- All Statistics ---");
        calculator.displayData();
        
        // Polymorphic execution - same interface, different implementations
        StatOperation meanCalc = new MeanCalculator(calculator.getDataList());
        StatOperation medianCalc = new MedianCalculator(calculator.getDataList());
        StatOperation modeCalc = new ModeCalculator(calculator.getDataList());
        
        System.out.println("Mean: " + meanCalc.calculate());
        System.out.println("Median: " + medianCalc.calculate());
        System.out.println("Mode: " + modeCalc.calculate());
    }
    
    /**
     * Clear all data
     */
    private void clearData() {
        calculator.clearData();
        System.out.println("All data cleared.");
    }
}
